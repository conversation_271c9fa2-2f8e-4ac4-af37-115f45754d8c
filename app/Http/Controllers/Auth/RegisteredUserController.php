<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Carbon\Carbon;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules;
use Illuminate\View\View;
use App\Models\Course;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {

        $input = $request->all();

        if (get_frontend_settings('recaptcha_status') == true && check_recaptcha($input['g-recaptcha-response']) == false) {

            Session::flash('error', 'Xác thực Recaptcha thất bại');

            return redirect(route('register.form'));
        }

        $validator = Validator::make($request->all(), [
            'name' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'unique:users,email'],
            'password' => ['required', Rules\Password::defaults()],
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        if (empty($request->name)) {
            $emailParts = explode('@', $request->email);
            $username = $emailParts[0];
            $request->merge(['name' => $username]);
        }
        $user_data = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => 'student',
            'status' => 1,
            'password' => Hash::make($request->password),
        ];

        // Lưu referral source từ parameter ?ref= hoặc cookie
        if ($request->has('ref')) {
            $user_data['source'] = $request->get('ref');
        } elseif ($request->cookie('affiliate_ref')) {
            $user_data['source'] = $request->cookie('affiliate_ref');
        }

        if (get_settings('student_email_verification') != 1) {
            $user_data['email_verified_at'] = Carbon::now();
        }

        $user = User::create($user_data);

        try {
            event(new Registered($user));
        } catch (\Exception $e) {
            Session::flash('error', 'Kết nối SMTP thất bại');
        }


        Auth::login($user);

        return redirect(RouteServiceProvider::HOME);
    }

    public function registerIncourse(Request $request)
    {

        $input = $request->all();

        if (get_frontend_settings('recaptcha_status') == true && check_recaptcha($input['g-recaptcha-response']) == false) {

            return response()->json([
                'error' => true,
                'message' => 'Xác thực Recaptcha thất bại'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'name' => ['nullable', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'unique:users,email'],
            'password' => ['required','min:8','max:200', Rules\Password::defaults()],
            'confirm_password' => ['required', 'same:password'],
        ], [
            'name.string' => 'Tên phải là chuỗi văn bản',
            'name.max' => 'Tên không được vượt quá 255 ký tự',
            'email.required' => 'Email là bắt buộc',
            'email.string' => 'Email phải là chuỗi văn bản',
            'email.email' => 'Email không đúng định dạng',
            'email.unique' => 'Email đã được sử dụng',
            'password.required' => 'Mật khẩu là bắt buộc',
            'password.min' => 'Mật khẩu ít nhất phải có 8 ký tự',
            'password.max' => 'Mật khẩu không được vượt quá 200 ký tự',
            'confirm_password.required' => 'Xác nhận mật khẩu là bắt buộc',
            'confirm_password.same' => 'Xác nhận mật khẩu không khớp với mật khẩu'
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors();
            // Get the first error message
            if ($errors->has('email') && $errors->first('email') === 'Email đã được sử dụng') {
                $errorMessage = 'Email đã được sử dụng';
            } else {
                $errorMessage = $errors->first();
            }

            return response()->json([
                'error' => true,
                'message' => $errors
            ], 422);
        }
        if (empty($request->name)) {
            $emailParts = explode('@', $request->email);
            $username = $emailParts[0];
            $request->merge(['name' => $username]);
        }
        $user_data = [
            'name' => $request->name,
            'email' => $request->email,
            'role' => 'student',
            'status' => 1,
            'password' => Hash::make($request->password),
        ];

        // Lưu referral source từ parameter ?ref= hoặc cookie
        if ($request->has('ref')) {
            $user_data['source'] = $request->get('ref');
        } elseif ($request->cookie('affiliate_ref')) {
            $user_data['source'] = $request->cookie('affiliate_ref');
        }

        if (get_settings('student_email_verification') != 1) {
            $user_data['email_verified_at'] = Carbon::now();
        }

        $user = User::create($user_data);

        try {
            event(new Registered($user));
        } catch (\Exception $e) {
            // Session::flash('error', 'Kết nối SMTP thất bại');
        }

        $course_details = null;
        if ($request->has('course_id')) {
            $course_details = Course::find($request->input('course_id'));
        }
        Auth::login($user);
        $enrollment_status = enroll_status(isset($course_details) && is_object($course_details) ? $course_details->id : 0, $user->id);

        return response()->json([
            'error' => false,
            'message' => 'Đăng ký thành công',
            'data' => [
                'html_profile' => view('ajax.html-menu-user-profile', ["user" => $user, "enrollment_status" => $enrollment_status, "course_details" => $course_details])->render(),
            ]
        ]);
    }

    public function registerAjax(Request $request)
    {

        try {

            $input = $request->all();

            if (get_frontend_settings('recaptcha_status') == true && check_recaptcha($input['g-recaptcha-response']) == false) {

                return response()->json([
                    'success' => false,
                    'message' => 'Xác thực Recaptcha thất bại'
                ], 422);
            }

            $validator = Validator::make($request->all(), [
                'name' => ['required', 'string', 'max:255'],
                "phone" => ['required', 'numeric'],
                'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
                'password' => ['required', Rules\Password::defaults()],
            ], [
                'name.required' => 'Tên là bắt buộc',
                'name.string' => 'Tên phải là chuỗi văn bản',
                'name.max' => 'Tên không được vượt quá 255 ký tự',
                'phone.required' => 'Số điện thoại là bắt buộc',
                'phone.numeric' => 'Số điện thoại phải là số',
                'email.required' => 'Email là bắt buộc',
                'email.string' => 'Email phải là chuỗi văn bản',
                'email.email' => 'Email không đúng định dạng',
                'email.max' => 'Email không được vượt quá 255 ký tự',
                'email.unique' => 'Email đã được sử dụng',
                'password.required' => 'Mật khẩu là bắt buộc'
            ]);

            if ($validator->fails()) {
                $errors = $validator->errors();
                $errorMessage = '';

                // Get the first error message
                if ($errors->has('email') && $errors->first('email') === 'Email đã được sử dụng') {
                    $errorMessage = 'Email đã được sử dụng';
                } else {
                    $errorMessage = $errors->first();
                }

                return response()->json([
                    'success' => false,
                    'message' => $errorMessage
                ], 422);
            }
            $user_data = [
                'name' => $request->input("name"),
                'email' => $request->input("email"),
                'phone' => $request->input("phone"),
                'role' => 'student',
                'status' => 1,
                'password' => Hash::make($request->input('password')),
            ];

            // Lưu referral source từ parameter ?ref= hoặc cookie
            if ($request->has('ref')) {
                $user_data['source'] = $request->get('ref');
            } elseif ($request->cookie('affiliate_ref')) {
                $user_data['source'] = $request->cookie('affiliate_ref');
            }

            if (get_settings('student_email_verification') != 1) {
                $user_data['email_verified_at'] = Carbon::now();
            }

            $user = User::create($user_data);

            try {
                event(new Registered($user));
            } catch (\Exception $e) {
                // Session::flash('error', 'Kết nối SMTP thất bại');
            }
            Auth::login($user, true);
            return response()->json([
                'success' => true,
                'message' => 'Đăng ký thành công',
                'url_redirect' => route("course.player", $request->input("course_slug")),
            ]);
        } catch (\Exception $e) {

            // Return error response
            return response()->json([
                'success' => false,
                'exception' => $e->getMessage(),
                'message' => 'Đã xảy ra lỗi khi tạo người dùng.',
            ], 500);
        }
    }
}
