<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Bootcamp;
use App\Models\BootcampPurchase;
use App\Models\CartItem;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\OfflinePayment;
use App\Models\Payment_history;
use App\Models\TeamPackagePurchase;
use App\Models\TeamTrainingPackage;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class OfflinePaymentController extends Controller
{
    public function index(Request $request)
    {
        $payments = OfflinePayment::with(['user', 'course', 'affiliater'])->orderBY('id', 'DESC');



        // Filter theo affiliate_id nếu có
        if ($request->filled('affiliate_id')) {
            $payments->where('affiliate_id', $request->affiliate_id);
        }

        // Tìm kiếm theo tên, email, khóa học
        if ($request->filled('search')) {
            $search = $request->search;
            $payments->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('course', function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%");
                })
                ->orWhere('transaction_content', 'like', "%{$search}%")
                ->orWhere('coupon', 'like', "%{$search}%");
            });
        }

        // Lọc theo trạng thái
        if ($request->filled('status') && $request->status != 'all') {
            if ($request->status == 'approved') {
                $payments->where('status', 1);
            } elseif ($request->status == 'suspended') {
                $payments->where('status', 2);
            } elseif ($request->status == 'pending') {
                $payments->where('status', 0)->orWhere('status', null);
            }
        }

        // Lọc theo loại item
        if ($request->filled('item_type') && $request->item_type != 'all') {
            $payments->where('item_type', $request->item_type);
        }

        // Lọc theo payment type
        if ($request->filled('payment_type') && $request->payment_type != 'all') {
            $payments->where('payment_type', $request->payment_type);
        }

        // Lọc theo khoảng thời gian
        if ($request->filled('date_from')) {
            try {
                $dateFrom = Carbon::parse($request->date_from)->startOfDay();
                $payments->where('created_at', '>=', $dateFrom);
            } catch (\Exception $e) {
                // Invalid date format, skip filter
            }
        }
        if ($request->filled('date_to')) {
            try {
                $dateTo = Carbon::parse($request->date_to)->endOfDay();
                $payments->where('created_at', '<=', $dateTo);
            } catch (\Exception $e) {
                // Invalid date format, skip filter
            }
        }

        // Lọc theo khoảng số tiền
        if ($request->filled('amount_from')) {
            $payments->where('total_amount', '>=', $request->amount_from);
        }
        if ($request->filled('amount_to')) {
            $payments->where('total_amount', '<=', $request->amount_to);
        }

        $page_data['payments'] = $payments->paginate(20)->appends($request->query());

        // Thống kê với cùng bộ lọc
        $statsQuery = OfflinePayment::query();

        // Áp dụng cùng bộ lọc cho thống kê
        if ($request->filled('affiliate_id')) {
            $statsQuery->where('affiliate_id', $request->affiliate_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $statsQuery->where(function($query) use ($search) {
                $query->whereHas('user', function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                })
                ->orWhereHas('course', function($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%");
                })
                ->orWhere('transaction_content', 'like', "%{$search}%")
                ->orWhere('coupon', 'like', "%{$search}%");
            });
        }

        if ($request->filled('item_type') && $request->item_type != 'all') {
            $statsQuery->where('item_type', $request->item_type);
        }

        if ($request->filled('payment_type') && $request->payment_type != 'all') {
            $statsQuery->where('payment_type', $request->payment_type);
        }

        if ($request->filled('date_from')) {
            try {
                $dateFrom = Carbon::parse($request->date_from)->startOfDay();
                $statsQuery->where('created_at', '>=', $dateFrom);
            } catch (\Exception $e) {
                // Invalid date format, skip filter
            }
        }
        if ($request->filled('date_to')) {
            try {
                $dateTo = Carbon::parse($request->date_to)->endOfDay();
                $statsQuery->where('created_at', '<=', $dateTo);
            } catch (\Exception $e) {
                // Invalid date format, skip filter
            }
        }

        if ($request->filled('amount_from')) {
            $statsQuery->where('total_amount', '>=', $request->amount_from);
        }
        if ($request->filled('amount_to')) {
            $statsQuery->where('total_amount', '<=', $request->amount_to);
        }

        // Tính thống kê dựa trên bộ lọc
        $page_data['total_payments'] = (clone $statsQuery)->count();
        $page_data['pending_payments'] = (clone $statsQuery)->where(function($q) {
            $q->where('status', 0)->orWhere('status', null);
        })->count();
        $page_data['approved_payments'] = (clone $statsQuery)->where('status', 1)->count();
        $page_data['suspended_payments'] = (clone $statsQuery)->where('status', 2)->count();
        $page_data['total_amount'] = (clone $statsQuery)->where('status', 1)->sum('total_amount');
        $page_data['total_affiliate_amount'] = (clone $statsQuery)->where('status', 1)->whereNotNull('affiliate_amount')->sum('affiliate_amount');



        return view('admin.offline_payments.index', $page_data);
    }



    public function download_doc($id)
    {
        // validate id
        if (empty($id)) {
            Session::flash('error', 'Không tìm thấy dữ liệu.');
            return redirect()->back();
        }

        // payment details
        $payment_details = OfflinePayment::where('id', $id)->first();
        $filePath = public_path($payment_details->doc);
        if (!file_exists($filePath)) {
            Session::flash('error', 'Không tìm thấy dữ liệu.');
            return redirect()->back();
        }
        // download file
        return Response::download($filePath);
    }

    public function accept_payment($id, $payment_type = null)
    {
        // validate id
        if (empty($id)) {
            Session::flash('error', 'ID không được để trống.');
            return redirect()->back();
        }

        // payment details
        $query = OfflinePayment::where('id', $id)->whereIn('status', [0,2]);

        if ($query->doesntExist()) {
            Session::flash('error', 'Không tìm thấy dữ liệu.');
            return redirect()->back();
        }

        $payment_details = $query->first();

        $payment['invoice'] = Str::random(20);
        $payment['user_id'] = $payment_details['user_id'];
        $payment['payment_type'] = $payment_type ? $payment_type : 'offline';
        $payment['coupon'] = $payment_details->coupon;

        if ($payment_details->item_type == 'course') {
            $items = json_decode($payment_details->items);
            foreach ($items as $item) {
                $accept_payment = null;
                if ($payment_details->item_type == 'course') {
                    $course = Course::where('id', $item)->first();
                    $payment['course_id'] = $course->id;
                    $payment['amount'] = $course->discount_flag == 1 ? $course->discounted_price : $course->price;
                    $payment['tax'] = $payment['amount'] * (get_settings('course_selling_tax') / 100);

                    if (get_course_creator_id($course->id)->role == 'admin') {
                        $payment['admin_revenue'] = $payment['amount'];
                    } else {
                        $payment['instructor_revenue'] = $payment['amount'] * (get_settings('instructor_revenue') / 100);
                        $payment['admin_revenue'] = $payment['amount'] - $payment['instructor_revenue'];
                    }
                    $accept_payment = Payment_history::insert($payment);
                    // start enroll user
                    if ($accept_payment) {
                        // Kiểm tra xem đã có enrollment hay chưa (lấy enrollment mới nhất)
                        $existingEnrollment = Enrollment::where('course_id', $course->id)
                            ->where('user_id', $payment_details['user_id'])
                            ->orderBy('id', 'desc')
                            ->first();

                        // Kiểm tra xem có phải là gia hạn không
                        $isRenewal = $existingEnrollment && enroll_status($course->id, $payment_details['user_id']) == 'expired';

                        if ($existingEnrollment) {
                            // Cập nhật enrollment hiện có (trường hợp gia hạn)
                            $updateData = [
                                'enrollment_type' => "paid",
                                'entry_date' => time(),
                                'updated_at' => date('Y-m-d H:i:s')
                            ];

                            // Thêm pricing_plan_id nếu có
                            if ($payment_details->pricing_plan_id && $course->hasMultiplePricingPlans()) {
                                $updateData['pricing_plan_id'] = $payment_details->pricing_plan_id;
                                $updateData['paid_amount'] = $payment_details->total_amount;

                                // Tính ngày hết hạn
                                $plan = $course->getPricingPlanById($payment_details->pricing_plan_id);
                                if ($plan) {
                                    if ($plan['duration_type'] === 'lifetime') {
                                        $updateData['expiry_date'] = null; // Trọn đời
                                    } elseif ($plan['duration_type'] === 'months' && isset($plan['duration_value'])) {
                                        // Luôn tính từ ngày hiện tại với số tháng chính xác
                                        $months = $plan['duration_value'];
                                        $updateData['expiry_date'] = strtotime("+" . $months . " months");
                                    }
                                }
                            } else {
                                // Logic cũ cho trường hợp không có pricing plan
                                if ($course->expiry_period > 0) {
                                    $months = $course->expiry_period;
                                    $updateData['expiry_date'] = strtotime("+" . $months . " months");
                                } else {
                                    $updateData['expiry_date'] = null;
                                }
                            }

                            $existingEnrollment->update($updateData);
                        } else {
                            // Xóa tất cả enrollment cũ trước khi tạo mới (để tránh duplicate)
                            Enrollment::where('course_id', $course->id)
                                ->where('user_id', $payment_details['user_id'])
                                ->delete();

                            // Tạo enrollment mới
                            $enroll['course_id'] = $course->id;
                            $enroll['user_id'] = $payment_details['user_id'];
                            $enroll['enrollment_type'] = "paid";
                            $enroll['entry_date'] = time();
                            $enroll['created_at'] = date('Y-m-d H:i:s');
                            $enroll['updated_at'] = date('Y-m-d H:i:s');

                            // Thêm pricing_plan_id nếu có
                            if ($payment_details->pricing_plan_id && $course->hasMultiplePricingPlans()) {
                                $enroll['pricing_plan_id'] = $payment_details->pricing_plan_id;
                                $enroll['paid_amount'] = $payment_details->total_amount;

                                // Tính ngày hết hạn theo plan
                                $plan = $course->getPricingPlanById($payment_details->pricing_plan_id);
                                if ($plan) {
                                    if ($plan['duration_type'] === 'lifetime') {
                                        $enroll['expiry_date'] = null; // Trọn đời
                                    } elseif ($plan['duration_type'] === 'months' && isset($plan['duration_value'])) {
                                        // Tính từ ngày hiện tại với số tháng chính xác
                                        $months = $plan['duration_value'];
                                        $enroll['expiry_date'] = strtotime("+" . $months . " months");
                                    }
                                }
                            } else {
                                // Logic cũ cho trường hợp không có pricing plan
                                if ($course->expiry_period > 0) {
                                    $months = $course->expiry_period;
                                    $enroll['expiry_date'] = strtotime("+" . $months . " months");
                                } else {
                                    $enroll['expiry_date'] = null;
                                }
                            }

                            // insert a new enrollment
                            Enrollment::insert($enroll);
                        }
                    }
                }
            }
        } elseif ($payment_details->item_type == 'bootcamp') {
            $bootcamps = Bootcamp::whereIn('id', json_decode($payment_details->items, true))->get();
            foreach ($bootcamps as $bootcamp) {
                $bootcamp_payment['invoice'] = '#' . Str::random(20);
                $bootcamp_payment['user_id'] = $payment_details['user_id'];
                $bootcamp_payment['bootcamp_id'] = $bootcamp->id;
                $bootcamp_payment['price'] = $bootcamp->discount_flag == 1 ? $bootcamp->price - $bootcamp->discounted_price : $bootcamp->price;
                $bootcamp_payment['tax'] = 0;
                $bootcamp_payment['payment_method'] = 'offline';
                $bootcamp_payment['status'] = 1;

                // insert bootcamp purchase
                BootcampPurchase::insert($bootcamp_payment);
            }
        } elseif ($payment_details->item_type == 'package') {
            $packages = TeamTrainingPackage::whereIn('id', json_decode($payment_details->items, true))->get();
            foreach ($packages as $package) {
                $package_payment['invoice'] = '#' . Str::random(20);
                $package_payment['user_id'] = $payment_details['user_id'];
                $package_payment['package_id'] = $package->id;
                $package_payment['price'] = $package->price;
                $package_payment['tax'] = 0;
                $package_payment['payment_method'] = 'offline';
                $package_payment['status'] = 1;

                // insert package purchase
                TeamPackagePurchase::insert($package_payment);
            }
        }

        // remove items from offline payment
        OfflinePayment::where('id', $id)->update(['status' => 1]);

        // go back
        Session::flash('success', 'Thanh toán đã được chấp nhận.');

        return redirect()->route('admin.offline.payments');
    }

    public function decline_payment($id)
    {
        // remove items from offline payment
        OfflinePayment::where('id', $id)->update(['status' => 2]);

        // go back
        Session::flash('success', 'Thanh toán đã bị tạm dừng');
        return redirect()->route('admin.offline.payments');
    }

    public function delete_payment($id)
    {
        OfflinePayment::where('id', $id)->delete();
        Session::flash('success', 'Xóa doanh thu admin thành công');
        return redirect()->route('admin.offline.payments');
    }
}
