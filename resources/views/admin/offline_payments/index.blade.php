@extends('layouts.admin')
@push('title', '<PERSON><PERSON><PERSON><PERSON> lý thanh toán offline')
@push('meta')@endpush
@push('css')
<style>
/* Table Styling */
.table {
    background: white;
    border: 1px solid #dee2e6;
}
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    padding: 12px;
    font-size: 0.875rem;
}
.table td {
    vertical-align: middle;
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
}
.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Card Styling */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}
.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}
.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Badge Styling */
.badge {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 500;
}
.badge-status {
    font-size: 0.8rem;
    padding: 8px 16px;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Avatar Styling */
.avatar-sm {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Purple color theme */
.bg-purple {
    background-color: #6f42c1 !important;
}
.text-purple {
    color: #6f42c1 !important;
}

/* Responsive adjustments for stats cards */
@media (max-width: 768px) {
    .stats-card {
        padding: 15px;
    }
    .stats-card h4 {
        font-size: 1.1rem;
    }
    .stats-card p {
        font-size: 0.8rem;
    }
    .avatar-sm {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}

/* Button Styling */
.btn-action {
    border-radius: 8px;
    padding: 8px 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* Form Styling */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: all 0.3s ease;
}
.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Search Form */
.search-form {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 25px;
}

/* Utilities */
.text-12px {
    font-size: 0.75rem;
}
.min-w-200px {
    min-width: 200px;
}
.row-number {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

/* Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Custom Dropdown */
.custom-dropdown .dropdown-menu {
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    border: none;
    padding: 10px 0;
}
.custom-dropdown .dropdown-item {
    padding: 10px 20px;
    transition: all 0.3s ease;
}
.custom-dropdown .dropdown-item:hover {
    background-color: #f8f9ff;
    color: #667eea;
}
</style>
@endpush
@section('content')
    <!-- Mani section header and breadcrumb -->
    <div class="ol-card radius-8px print-d-none">
        <div class="ol-card-body px-20px my-3 py-4">
            <div class="d-flex align-items-center justify-content-between flex-md-nowrap flex-wrap gap-3">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    <span>Quản lý thanh toán offline</span>
                </h4>
            </div>
        </div>
    </div>

    <!-- Thống kê -->
    <div class="row mb-4 print-d-none fade-in-up">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avatar-sm bg-primary text-white">
                            <i class="fi-rr-credit-card"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="text-muted mb-1 fw-medium">Tổng thanh toán</p>
                        <h4 class="mb-0 fw-bold text-dark">{{ number_format($total_payments) }}</h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avatar-sm bg-warning text-white">
                            <i class="fi-rr-clock"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="text-muted mb-1 fw-medium">Chờ duyệt</p>
                        <h4 class="mb-0 fw-bold text-warning">{{ number_format($pending_payments) }}</h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avatar-sm bg-success text-white">
                            <i class="fi-rr-check"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="text-muted mb-1 fw-medium">Đã duyệt</p>
                        <h4 class="mb-0 fw-bold text-success">{{ number_format($approved_payments) }}</h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avatar-sm bg-info text-white">
                            <i class="fi-rr-dollar"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="text-muted mb-1 fw-medium">Tổng doanh thu</p>
                        <h4 class="mb-0 fw-bold text-info">{{ currency($total_amount) }}</h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
            <div class="stats-card">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="avatar-sm bg-purple text-white">
                            <i class="fi-rr-hand-holding-usd"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <p class="text-muted mb-1 fw-medium">Tổng hoa hồng</p>
                        <h4 class="mb-0 fw-bold text-purple">{{ currency($total_affiliate_amount) }}</h4>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="ol-card">
                <div class="ol-card-body p-3">
                    <!-- Tìm kiếm và bộ lọc -->
                    <div class="ol-card print-d-none mb-3">
                        <div class="ol-card-body">
                            <h6 class="mb-3">Tìm kiếm & Bộ lọc</h6>
                            <form action="{{ route('admin.offline.payments') }}" method="get" class="row g-3">
                                <!-- Tìm kiếm -->
                                <div class="col-md-4">
                                    <label class="form-label">Tìm kiếm</label>
                                    <input type="text" class="form-control" name="search"
                                           value="{{ request('search') }}"
                                           placeholder="Tên, email, khóa học, mã giao dịch...">
                                </div>

                                <!-- Trạng thái -->
                                <div class="col-md-2">
                                    <label class="form-label">Trạng thái</label>
                                    <select class="form-control" name="status">
                                        <option value="all">Tất cả</option>
                                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Chờ duyệt</option>
                                        <option value="approved" {{ request('status') == 'approved' ? 'selected' : '' }}>Đã duyệt</option>
                                        <option value="suspended" {{ request('status') == 'suspended' ? 'selected' : '' }}>Đã từ chối</option>
                                    </select>
                                </div>

                                <!-- Loại item -->
                                <div class="col-md-2">
                                    <label class="form-label">Loại</label>
                                    <select class="form-control" name="item_type">
                                        <option value="all">Tất cả</option>
                                        <option value="course" {{ request('item_type') == 'course' ? 'selected' : '' }}>Khóa học</option>
                                        <option value="bootcamp" {{ request('item_type') == 'bootcamp' ? 'selected' : '' }}>Bootcamp</option>
                                        <option value="package" {{ request('item_type') == 'package' ? 'selected' : '' }}>Package</option>
                                    </select>
                                </div>

                                <!-- Payment Type -->
                                <div class="col-md-2">
                                    <label class="form-label">Phương thức</label>
                                    <select class="form-control" name="payment_type">
                                        <option value="all">Tất cả</option>
                                        <option value="offline" {{ request('payment_type') == 'offline' ? 'selected' : '' }}>Offline</option>
                                        <option value="sepay" {{ request('payment_type') == 'sepay' ? 'selected' : '' }}>SePay</option>
                                        <option value="bank" {{ request('payment_type') == 'bank' ? 'selected' : '' }}>Chuyển khoản</option>
                                    </select>
                                </div>

                                <!-- Từ ngày -->
                                <div class="col-md-3">
                                    <label class="form-label">Từ ngày</label>
                                    <input type="date" class="form-control" name="date_from"
                                           value="{{ request('date_from') }}">
                                </div>

                                <!-- Đến ngày -->
                                <div class="col-md-3">
                                    <label class="form-label">Đến ngày</label>
                                    <input type="date" class="form-control" name="date_to"
                                           value="{{ request('date_to') }}">
                                </div>

                                <!-- Buttons -->
                                <div class="col-12">
                                    <div class="d-flex gap-2 flex-wrap">
                                        <button type="submit" class="btn ol-btn-primary">Tìm kiếm</button>
                                        <a href="{{ route('admin.offline.payments') }}" class="btn ol-btn-light">Làm mới</a>
                                        <div class="custom-dropdown">
                                            <button type="button" class="dropdown-header btn ol-btn-light"
                                                    data-bs-toggle="dropdown">Xuất file</button>
                                            <ul class="dropdown-list">
                                                <li>
                                                    <a class="dropdown-item" href="#"
                                                        onclick="downloadPDF('.print-table', 'offline-payments')">PDF</a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item" href="#" onclick="window.print();">In</a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>



                    <!-- Table -->
                    @if (count($payments) > 0)
                        <div
                            class="admin-tInfo-pagi d-flex justify-content-between justify-content-center align-items-center gr-15 flex-wrap">
                            <p class="admin-tInfo">
                                Hiển thị {{ count($payments) }} trong tổng số {{ $payments->total() }} bản ghi
                            </p>
                        </div>
                        <div class="table-responsive course_list" id="course_list">
                            <table class="eTable eTable-2 print-table table">
                                <thead>
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Khách hàng</th>
                                        <th scope="col">Sản phẩm</th>
                                        <th scope="col">Loại</th>
                                        <th scope="col">Thời hạn</th>
                                        <th scope="col">Số tiền</th>
                                        <th scope="col">Phương thức</th>
                                        <th scope="col">Ngày tạo</th>
                                        <th scope="col">Mã giao dịch</th>
                                        <th scope="col">Coupon</th>
                                        <th scope="col">Affiliate</th>
                                        <th scope="col">Hoa hồng</th>
                                        <th scope="col">Trạng thái</th>
                                        <th scope="col" class="print-d-none">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($payments as $key => $payment)
                                        <tr>
                                            <th scope="row">
                                                <p class="row-number">{{ $payments->firstItem() + $key }}</p>
                                            </th>

                                            <!-- Khách hàng -->
                                            <td>
                                                <div class="min-w-200px">
                                                    <div class="fw-medium text-dark">
                                                        {{ $payment->user->name ?? 'N/A' }}
                                                    </div>
                                                    <div class="text-12px text-muted">
                                                        {{ $payment->user->email ?? 'N/A' }}
                                                    </div>
                                                    <div class="text-12px text-muted">
                                                        {{ $payment->user->phone ?? 'N/A' }}
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Sản phẩm -->
                                            <td>
                                                <div class="min-w-200px">
                                                    @if ($payment->item_type == 'course')
                                                        @foreach (App\Models\Course::whereIn('id', json_decode($payment->items, true))->get() as $course)
                                                            <div class="mb-1">
                                                                <a href="{{ route('course.details', slugify($course->title)) }}"
                                                                   class="text-primary text-decoration-none fw-medium">
                                                                    {{ Str::limit($course->title, 40) }}
                                                                </a>
                                                            </div>
                                                        @endforeach
                                                    @elseif ($payment->item_type == 'bootcamp')
                                                        @foreach (App\Models\Bootcamp::whereIn('id', json_decode($payment->items, true))->get() as $bootcamp)
                                                            <div class="mb-1">
                                                                <a href="{{ route('bootcamp.details', ['slug' => slugify($bootcamp->title)]) }}"
                                                                   class="text-primary text-decoration-none fw-medium">
                                                                    {{ Str::limit($bootcamp->title, 40) }}
                                                                </a>
                                                            </div>
                                                        @endforeach
                                                    @elseif ($payment->item_type == 'package')
                                                        @foreach (App\Models\TeamTrainingPackage::whereIn('id', json_decode($payment->items, true))->get() as $package)
                                                            <div class="mb-1">
                                                                <a href="{{ route('team.package.details', ['slug' => $package->slug]) }}"
                                                                   class="text-primary text-decoration-none fw-medium">
                                                                    {{ Str::limit($package->title, 40) }}
                                                                </a>
                                                            </div>
                                                        @endforeach
                                                    @endif
                                                </div>
                                            </td>

                                            <!-- Loại -->
                                            <td>
                                                @if ($payment->item_type == 'course')
                                                    <span class="badge bg-primary">Khóa học</span>
                                                @elseif ($payment->item_type == 'bootcamp')
                                                    <span class="badge bg-info">Bootcamp</span>
                                                @elseif ($payment->item_type == 'package')
                                                    <span class="badge bg-secondary">Package</span>
                                                @endif
                                            </td>

                                            <!-- Thời hạn -->
                                            <td>
                                                @if ($payment->item_type == 'course' && $payment->pricing_plan_id)
                                                    @php
                                                        $course = App\Models\Course::find($payment->course_id);
                                                        if ($course) {
                                                            $plan = $course->getPricingPlanById($payment->pricing_plan_id);
                                                            if ($plan) {
                                                                if ($plan['duration_type'] === 'lifetime') {
                                                                    echo '<span class="badge bg-success">Trọn đời</span>';
                                                                } elseif ($plan['duration_type'] === 'months') {
                                                                    $months = $plan['duration_value'] ?? 0;
                                                                    echo '<span class="badge bg-info">' . $months . ' tháng</span>';
                                                                } else {
                                                                    echo '<span class="badge bg-secondary">Không xác định</span>';
                                                                }
                                                            } else {
                                                                echo '<span class="badge bg-warning">Plan không tồn tại</span>';
                                                            }
                                                        } else {
                                                            echo '<span class="text-muted">-</span>';
                                                        }
                                                    @endphp
                                                @else
                                                    @if ($payment->item_type == 'course')
                                                        <span class="badge bg-success">Trọn đời</span>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                @endif
                                            </td>

                                            <!-- Số tiền -->
                                            <td>
                                                <div class="fw-bold text-success">
                                                    {{ currency($payment->total_amount) }}
                                                </div>
                                            </td>

                                            <!-- Phương thức -->
                                            <td>
                                                @if ($payment->payment_type == 'offline')
                                                    <span class="badge bg-secondary">Offline</span>
                                                @elseif ($payment->payment_type == 'sepay')
                                                    <span class="badge bg-primary">SePay</span>
                                                @else
                                                    <span class="badge bg-info">{{ ucfirst($payment->payment_type) }}</span>
                                                @endif
                                            </td>

                                            <!-- Ngày tạo -->
                                            <td>
                                                <div class="text-12px">
                                                    <div>{{ date('d/m/Y', strtotime($payment->created_at)) }}</div>
                                                    <div class="text-muted">{{ date('H:i', strtotime($payment->created_at)) }}</div>
                                                </div>
                                            </td>

                                            <!-- Mã giao dịch -->
                                            <td>
                                                <div class="text-12px">
                                                    @if($payment->transaction_content)
                                                        <code>{{ $payment->transaction_content }}</code>
                                                    @else
                                                        <span class="text-muted">-</span>
                                                    @endif
                                                </div>
                                            </td>

                                            <!-- Coupon -->
                                            <td>
                                                @if($payment->coupon)
                                                    <span class="badge bg-warning text-dark">{{ $payment->coupon }}</span>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>

                                            <!-- Affiliate -->
                                            <td>
                                                @if($payment->affiliater)
                                                    <div class="text-12px">
                                                        <div class="fw-medium">{{ $payment->affiliater->name }}</div>
                                                        <div class="text-muted">{{ $payment->affiliater->email }}</div>
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>

                                            <!-- Hoa hồng -->
                                            <td>
                                                @if($payment->affiliate_amount)
                                                    <div class="text-success fw-medium">
                                                        {{ currency($payment->affiliate_amount) }}
                                                    </div>
                                                @else
                                                    <span class="text-muted">-</span>
                                                @endif
                                            </td>

                                            <!-- Trạng thái -->
                                            <td>
                                                @if ($payment->status == 1)
                                                    <span class="badge bg-success">Đã duyệt</span>
                                                @elseif($payment->status == 2)
                                                    <span class="badge bg-danger">Đã từ chối</span>
                                                @else
                                                    <span class="badge bg-warning text-dark">Chờ duyệt</span>
                                                @endif
                                            </td>

                                            <!-- Thao tác -->
                                            <td class="print-d-none">
                                                <div class="d-flex gap-1">
                                                    @if ($payment->status != 1)
                                                        <button type="button"
                                                                class="btn btn-sm btn-success"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#approveModal{{ $payment->id }}">
                                                            Duyệt
                                                        </button>
                                                    @endif

                                                    @if ($payment->status != 2 && $payment->status != 1)
                                                        <button type="button"
                                                                class="btn btn-sm btn-warning"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#declineModal{{ $payment->id }}">
                                                            Từ chối
                                                        </button>
                                                    @endif

                                                    @if($payment->doc)
                                                        <a href="{{ route('admin.offline.payment.doc', $payment->id) }}"
                                                           class="btn btn-sm btn-info">
                                                            Tải file
                                                        </a>
                                                    @endif

                                                    <div class="dropdown">
                                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                                                type="button" data-bs-toggle="dropdown">
                                                            Khác
                                                        </button>
                                                        <ul class="dropdown-menu">
                                                            <li>
                                                                <h6 class="dropdown-header">Chi tiết</h6>
                                                            </li>
                                                            <li>
                                                                <span class="dropdown-item-text">
                                                                    <small>ID: #{{ $payment->id }}</small>
                                                                </span>
                                                            </li>
                                                            @if($payment->invoice)
                                                                <li>
                                                                    <span class="dropdown-item-text">
                                                                        <small>Invoice: {{ $payment->invoice }}</small>
                                                                    </span>
                                                                </li>
                                                            @endif
                                                            @if ($payment->item_type == 'course')
                                                                <li>
                                                                    <span class="dropdown-item-text">
                                                                        <small>Thời hạn:
                                                                            @if ($payment->pricing_plan_id)
                                                                                @php
                                                                                    $course = App\Models\Course::find($payment->course_id);
                                                                                    if ($course) {
                                                                                        $plan = $course->getPricingPlanById($payment->pricing_plan_id);
                                                                                        if ($plan) {
                                                                                            if ($plan['duration_type'] === 'lifetime') {
                                                                                                echo 'Trọn đời';
                                                                                            } elseif ($plan['duration_type'] === 'months') {
                                                                                                $months = $plan['duration_value'] ?? 0;
                                                                                                echo $months . ' tháng';
                                                                                            }
                                                                                        } else {
                                                                                            echo 'Plan không tồn tại';
                                                                                        }
                                                                                    } else {
                                                                                        echo 'Không xác định';
                                                                                    }
                                                                                @endphp
                                                                            @else
                                                                                Trọn đời
                                                                            @endif
                                                                        </small>
                                                                    </span>
                                                                </li>
                                                            @endif
                                                            <li><hr class="dropdown-divider"></li>
                                                            <li>
                                                                <button type="button" class="dropdown-item text-danger"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#deleteModal{{ $payment->id }}">
                                                                    Xóa
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>

                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Modals for each payment -->
                        @foreach ($payments as $payment)
                            <!-- Approve Modal -->
                            <div class="modal fade" id="approveModal{{ $payment->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content border-0 shadow-lg">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Xác nhận duyệt thanh toán</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <h6>Bạn có chắc chắn muốn duyệt thanh toán này?</h6>
                                            </div>
                                            <div class="bg-light rounded p-3 mb-3">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <small class="text-muted">Khách hàng:</small>
                                                        <div class="fw-medium">{{ $payment->user->name ?? 'N/A' }}</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Số tiền:</small>
                                                        <div class="fw-bold text-success">{{ currency($payment->total_amount) }}</div>
                                                    </div>
                                                    @if ($payment->item_type == 'course' && $payment->pricing_plan_id)
                                                        <div class="col-6">
                                                            <small class="text-muted">Thời hạn:</small>
                                                            @php
                                                                $course = App\Models\Course::find($payment->course_id);
                                                                if ($course) {
                                                                    $plan = $course->getPricingPlanById($payment->pricing_plan_id);
                                                                    if ($plan) {
                                                                        if ($plan['duration_type'] === 'lifetime') {
                                                                            echo '<div class="fw-medium text-success">Trọn đời</div>';
                                                                        } elseif ($plan['duration_type'] === 'months') {
                                                                            $months = $plan['duration_value'] ?? 0;
                                                                            echo '<div class="fw-medium text-info">' . $months . ' tháng</div>';
                                                                        }
                                                                    } else {
                                                                        echo '<div class="fw-medium text-warning">Plan không tồn tại</div>';
                                                                    }
                                                                } else {
                                                                    echo '<div class="fw-medium text-muted">-</div>';
                                                                }
                                                            @endphp
                                                        </div>
                                                    @elseif ($payment->item_type == 'course')
                                                        <div class="col-6">
                                                            <small class="text-muted">Thời hạn:</small>
                                                            <div class="fw-medium text-success">Trọn đời</div>
                                                        </div>
                                                    @endif
                                                    <div class="col-12">
                                                        <small class="text-muted">Mã giao dịch:</small>
                                                        <div class="fw-medium">{{ $payment->transaction_content ?? 'N/A' }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-info">
                                                <small>Sau khi duyệt, khách hàng sẽ được cấp quyền truy cập vào khóa học và không thể hoàn tác.</small>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                            <a href="{{ route('admin.offline.payment.accept', $payment->id) }}"
                                               class="btn btn-success">Xác nhận duyệt</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Decline Modal -->
                            <div class="modal fade" id="declineModal{{ $payment->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content border-0 shadow-lg">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Xác nhận từ chối thanh toán</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <h6>Bạn có chắc chắn muốn từ chối thanh toán này?</h6>
                                            </div>
                                            <div class="bg-light rounded p-3 mb-3">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <small class="text-muted">Khách hàng:</small>
                                                        <div class="fw-medium">{{ $payment->user->name ?? 'N/A' }}</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Số tiền:</small>
                                                        <div class="fw-bold text-danger">{{ currency($payment->total_amount) }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-warning">
                                                <small>Thanh toán sẽ bị từ chối và khách hàng sẽ không được cấp quyền truy cập.</small>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                            <a href="{{ route('admin.offline.payment.decline', $payment->id) }}"
                                               class="btn btn-warning">Xác nhận từ chối</a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Delete Modal -->
                            <div class="modal fade" id="deleteModal{{ $payment->id }}" tabindex="-1" aria-hidden="true">
                                <div class="modal-dialog modal-dialog-centered">
                                    <div class="modal-content border-0 shadow-lg">
                                        <div class="modal-header">
                                            <h5 class="modal-title">Xác nhận xóa thanh toán</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="mb-3">
                                                <h6 class="text-danger">Cảnh báo: Hành động này không thể hoàn tác!</h6>
                                            </div>
                                            <div class="bg-light rounded p-3 mb-3">
                                                <div class="row g-2">
                                                    <div class="col-6">
                                                        <small class="text-muted">ID:</small>
                                                        <div class="fw-medium">#{{ $payment->id }}</div>
                                                    </div>
                                                    <div class="col-6">
                                                        <small class="text-muted">Số tiền:</small>
                                                        <div class="fw-bold">{{ currency($payment->total_amount) }}</div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="alert alert-danger">
                                                <small>Bản ghi thanh toán sẽ bị xóa vĩnh viễn khỏi hệ thống.</small>
                                            </div>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                            <a href="{{ route('admin.offline.payment.delete', $payment->id) }}"
                                               class="btn btn-danger">Xác nhận xóa</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        @include('admin.no_data')
                    @endif
                    <!-- Data info and Pagination -->
                    @if (count($payments) > 0)
                        <div class="admin-tInfo-pagi d-flex justify-content-between align-items-center gr-15 flex-wrap mt-3">
                            <div class="admin-tInfo">
                                <p class="mb-0">
                                    Hiển thị {{ $payments->firstItem() }} - {{ $payments->lastItem() }}
                                    trong tổng số {{ $payments->total() }} bản ghi
                                </p>
                                @if(request()->hasAny(['search', 'status', 'item_type', 'payment_type', 'date_from', 'date_to']))
                                    <small class="text-muted">
                                        (Đã lọc từ {{ $total_payments }} bản ghi)
                                    </small>
                                @endif
                            </div>
                            <div>
                                {{ $payments->links() }}
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection()

@push('js')
<script>
$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add loading state to action buttons
    $('.btn-action').on('click', function() {
        if (!$(this).hasClass('dropdown-toggle')) {
            var $btn = $(this);
            var originalHtml = $btn.html();

            $btn.html('<i class="fi-rr-spinner spin me-1"></i> Đang xử lý...');
            $btn.prop('disabled', true);

            // Re-enable after 3 seconds (in case of error)
            setTimeout(function() {
                $btn.html(originalHtml);
                $btn.prop('disabled', false);
            }, 3000);
        }
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // Add animation to table rows
    $('.table tbody tr').each(function(index) {
        $(this).css('animation-delay', (index * 0.1) + 's');
        $(this).addClass('fade-in-up');
    });

    // Smooth scroll to top when pagination is clicked
    $('.pagination a').on('click', function() {
        $('html, body').animate({
            scrollTop: 0
        }, 500);
    });

    // Add search input focus effect
    $('input[name="search"]').on('focus', function() {
        $(this).parent().addClass('focused');
    }).on('blur', function() {
        $(this).parent().removeClass('focused');
    });

    // Form validation
    $('form').on('submit', function() {
        var hasValue = false;
        $(this).find('input, select').each(function() {
            if ($(this).val() && $(this).val() !== 'all') {
                hasValue = true;
                return false;
            }
        });

        if (!hasValue) {
            // Show all records if no filter is applied
            return true;
        }
    });
});

// Add CSS for spinner animation
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    .focused {
        transform: scale(1.02);
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>
@endpush
